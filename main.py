# run_regions.py
# ----------------
# Minimal viewer: 2 models, detect theo vùng, vẽ bounding box/label.
# Yêu cầu: pip install ultralytics shapely opencv-python pyyaml

import os
import cv2
import yaml
import time
import numpy as np
from shapely.geometry import Point, Polygon
from ultralytics.nn.tasks import DetectionModel
import torch
from torch.nn import Sequential

from ultralytics import YOLO

# Monkey patch torch.load to use weights_only=False for compatibility
original_torch_load = torch.load
def patched_torch_load(f, map_location=None, pickle_module=None, weights_only=None, **kwargs):
    if weights_only is None:
        weights_only = False
    return original_torch_load(f, map_location=map_location, pickle_module=pickle_module, weights_only=weights_only, **kwargs)
torch.load = patched_torch_load

# ====== CLASS IDS (điều chỉnh nếu model của bạn dùng id khác) ======
GLOVE = 0
NOGLOVE = 1
HAND = 2
PEOPLE = 3          # (model All)
ROLL   = 4          # (model All)
SCISSOR = 3         # (model Keo 4-class)

# ==================== UTILITIES ====================
def load_config(path="config.yaml"):
    with open(path, "r", encoding="utf-8") as f:
        return yaml.load(f, Loader=yaml.FullLoader)

def points_from_cfg_area(area_cfg):
    """area_cfg: {'Count': N, 'point1': '(x,y)', ...} -> list[(x,y)]"""
    pts = []
    for i in range(area_cfg['Count']):
        s = area_cfg[f'point{i+1}']
        # s dạng '(x, y)' -> tuple(int,int)
        xy = eval(s) if isinstance(s, str) else tuple(s)
        pts.append(xy)
    return pts

def rect_from_tuple_str(s):
    # '(x1,y1,x2,y2)' -> [x1,y1,x2,y2]
    if isinstance(s, str):
        s = s.strip('()')
        return list(map(int, s.split(',')))
    return list(s)

def draw_polygon(img, polygon_pts, color, thickness=2):
    if len(polygon_pts) >= 3:
        arr = np.array(polygon_pts, dtype=np.int32)
        cv2.polylines(img, [arr], isClosed=True, color=color, thickness=thickness)

def put_label(img, text, xy, color, scale=0.7, thickness=2):
    cv2.putText(img, text, (int(xy[0]), int(xy[1]-5)), cv2.FONT_HERSHEY_SIMPLEX, scale, color, thickness, cv2.LINE_AA)

def bbox_center(b):
    x1, y1, x2, y2 = b
    return ( (x1+x2)/2.0, (y1+y2)/2.0 )

def inside(polygon: Polygon, box):
    """Check center point of box inside polygon"""
    cx, cy = bbox_center(box)
    return polygon.contains(Point(cx, cy))

# ==================== MAIN PIPELINE ====================
def main(cfg_path="config.yaml"):
    cfg = load_config(cfg_path)

    # ---- GENERAL ----
    src = cfg["general"]["source"]
    device = cfg["general"].get("device", "cpu")  # "cuda:0" or "cpu"
    show  = cfg["general"].get("show", True)

    # ---- COLORS ----
    colors_cfg = cfg["model_All"]["colors"]
    COL_NG   = eval(colors_cfg["NG"])
    COL_OK   = eval(colors_cfg["OK"])
    COL_OBJ  = eval(colors_cfg["OBJECT"])
    COL_CAB  = eval(colors_cfg["CABIN"])
    COL_PANEL= eval(colors_cfg["PANEL"])
    COL_FLOOR= eval(colors_cfg["FLOOR"])
    COL_SCIS = eval(colors_cfg["SCISSORCHECK"])
    COL_GATH = eval(colors_cfg["GATHER"])

    # ---- REGIONS / POLYGONS ----
    # accept_roll: roll area
    accept_roll_pts = points_from_cfg_area(cfg["accept_roll"])
    poly_roll = Polygon(accept_roll_pts)

    # optional: Scissor_warning, cabin, panel...
    cabin_pts  = points_from_cfg_area(cfg["cabin"])
    panel_pts  = points_from_cfg_area(cfg["panel"])
    scis_pts   = points_from_cfg_area(cfg["Scissor_warning"])

    poly_cabin = Polygon(cabin_pts)
    poly_panel = Polygon(panel_pts)
    poly_scis  = Polygon(scis_pts)


    # ---- MODELS ----
    w_all  = cfg["model_All"]["weights"]
    w_keo  = cfg["model_All"]["weights_Keo"]
    imgsz  = int(cfg["model_All"].get("imgsz", 640))
    conf_a = float(cfg["model_All"].get("conf", 0.5))
    iou_a  = float(cfg["model_All"].get("iou", 0.5))

    # Load models
    model_all = YOLO(w_all).to(device)
    model_keo = YOLO(w_keo).to(device)

    # ---- VIDEO ----
    cap = cv2.VideoCapture(src)
    if not cap.isOpened():
        print("Không mở được nguồn:", src)
        return

    prev = time.time()
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        H, W = frame.shape[:2]
        vis = frame.copy()

        # ======= RUN MODEL ALL (People + Roll) =======
        res_all = model_all.predict(source=frame, imgsz=imgsz, conf=conf_a, iou=iou_a, verbose=False)
        if res_all and len(res_all):
            r = res_all[0]
            if r.boxes is not None:
                for b in r.boxes:
                    box = b.xyxy[0].cpu().numpy().astype(int)  # [x1,y1,x2,y2]
                    score = float(b.conf.cpu().numpy())
                    cls = int(b.cls.cpu().numpy())

                    # PEOPLE: vẽ trên toàn khung hình
                    if cls == PEOPLE:
                        cv2.rectangle(vis, (box[0],box[1]), (box[2],box[3]), COL_OBJ, 2)
                        put_label(vis, f"People {score*100:.1f}%", (box[0], box[1]), COL_OBJ)

                    # ROLL/COPPER: CHỈ vẽ nếu nằm trong accept_roll
                    elif cls == ROLL:
                        if inside(poly_roll, box):
                            cv2.rectangle(vis, (box[0],box[1]), (box[2],box[3]), COL_OK, 2)
                            put_label(vis, f"Roll {score*100:.1f}%", (box[0], box[1]), COL_OK)
                        else:
                            # bên ngoài vùng -> có thể bỏ qua hoặc vẽ màu NG để kiểm chứng
                            cv2.rectangle(vis, (box[0],box[1]), (box[2],box[3]), COL_NG, 2)
                            put_label(vis, f"Roll(out) {score*100:.1f}%", (box[0], box[1]), COL_NG)

        # ======= RUN MODEL KEO (Glove/NoGlove/Hand/Scissor) =======
        res_keo = model_keo.predict(source=frame, imgsz=imgsz, conf=conf_a, iou=iou_a, verbose=False)
        if res_keo and len(res_keo):
            r2 = res_keo[0]
            if r2.boxes is not None:
                for b in r2.boxes:
                    box = b.xyxy[0].cpu().numpy().astype(int)
                    score = float(b.conf.cpu().numpy())
                    cls = int(b.cls.cpu().numpy())
                    cx, cy = bbox_center(box)

                    # Kiểm vùng theo loại:
                    # - Glove/NoGlove/Hand: trong cabin hoặc panel (tùy bạn muốn giới hạn vùng nào)
                    if cls in (GLOVE, NOGLOVE, HAND):
                        in_cabin = poly_cabin.contains(Point(cx, cy))
                        in_panel = poly_panel.contains(Point(cx, cy))
                        if in_cabin or in_panel:
                            color = COL_OK if cls in (GLOVE, HAND) else COL_NG
                            name = "Glove" if cls == GLOVE else ("NoGlove" if cls == NOGLOVE else "Hand")
                            cv2.rectangle(vis, (box[0],box[1]), (box[2],box[3]), color, 2)
                            put_label(vis, f"{name} {score*100:.1f}%", (box[0], box[1]), color)

                    # - Scissor: chỉ quan tâm trong vùng Scissor_warning
                    elif cls == SCISSOR:
                        if poly_scis.contains(Point(cx, cy)):
                            cv2.rectangle(vis, (box[0],box[1]), (box[2],box[3]), COL_SCIS, 2)
                            put_label(vis, f"Scissor {score*100:.1f}%", (box[0], box[1]), COL_SCIS)

        # ======= VẼ VÙNG THAM CHIẾU (tuỳ chọn) =======
        draw_polygon(vis, accept_roll_pts, COL_FLOOR, 2)
        draw_polygon(vis, cabin_pts, COL_CAB, 2)
        draw_polygon(vis, panel_pts, COL_PANEL, 2)
        draw_polygon(vis, scis_pts, COL_SCIS, 2)

        # ======= FPS =======
        now = time.time()
        fps = 1.0 / max(1e-6, now - prev)
        prev = now
        cv2.putText(vis, f"FPS: {fps:.1f}", (10,30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255,255,255), 2)

        if show:
            cv2.imshow("Regions Viewer", vis)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break

    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    # Mặc định đọc config.yaml cùng thư mục. Có thể truyền đường dẫn khác:
    # python run_regions.py  (hoặc chỉnh tay cfg_path ở dưới)
    main("config.yaml")
